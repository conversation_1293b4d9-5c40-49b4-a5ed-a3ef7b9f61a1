import { _decorator, Component, resources, Sprite, SpriteFrame } from 'cc';
const { ccclass } = _decorator;

@ccclass('SettingToggle')
export class SettingToggle extends Component {


    private isOn: boolean = true;

    start() {
        this.node.on("click", this.onClick, this);
        console.log("setting toggle start");
    }

    // 点击事件
    onClick() {
        console.log("setting toggle click");
        if (this.isOn) {
            resources.load("gameUI/关/spriteFrame", SpriteFrame, (err, spriteFrame) => {
                this.node.getComponent(Sprite).spriteFrame = spriteFrame;
            }); 
            this.isOn = false;
        }
        else {
            resources.load("gameUI/开/spriteFrame", SpriteFrame, (err, spriteFrame) => {
                this.node.getComponent(Sprite).spriteFrame = spriteFrame;
            }); 
            this.isOn = true;
        }
    }

    update(deltaTime: number) {

    }
}


