import { _decorator, Component, Label, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('provinceFab')
export class provinceFab extends Component {
    start() {

    }

    set(province: string, rank: number, rate: string) {
        this.node.getChildByName("province").getComponent(Label).string = `${province}省`;
        this.node.getChildByName("rank").getComponent(Label).string = `第${rank.toString()}名`;
        this.node.getChildByName("rate").getComponent(Label).string = `胜率：${rate}`;

    }
    update(deltaTime: number) {

    }
}


