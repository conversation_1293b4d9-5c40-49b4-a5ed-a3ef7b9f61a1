import { _decorator, Component, Node, RigidBody2D, Vec<PERSON>, sp, director, Collider2D, Contact2DType, IPhysics2DContact } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('MarbleSpine')
export class MarbleSpine extends Component {

    @property(Node)
    marble: Node = null;

    @property(sp.Skeleton)
    launchEffect: sp.Skeleton = null;

    @property(sp.Skeleton)
    collisionEffect: sp.Skeleton = null;

    // private spineSkeleton: sp.Skeleton = null;
    private effectActive: boolean = false;
    private lastCollisionTime: number = 0; // 上次碰撞时间
    private collisionCooldown: number = 1.0; // 碰撞特效冷却时间（秒）

    start() {
        // this.spineSkeleton = this.node.getComponent(sp.Skeleton);
        this.node.active = false;
        this.setupCollisionListener();

        // 监听弹珠发射事件
        director.on("aiMove", this.onMarbleLaunched, this);
        
        director.on('aiStop', this.onAIStop, this);
    }
    
    // 注册碰撞监听
    setupCollisionListener() {
        const collider = this.marble.getComponent(Collider2D);
        if (collider) {
            collider.on(Contact2DType.BEGIN_CONTACT, this.onCollisionEnter, this);
        }
    }

    onAIStop() {
        this.effectActive = false;
        this.node.active = false;
        console.log("特效禁用 - AI停止");
    }
    
    // 弹珠发射时启用特效
    onMarbleLaunched() {
        this.effectActive = true;
        this.node.active = true;

        // 播放发射特效
        this.playLaunchEffect();

        console.log("特效启用 - AI弹珠发射");
    }

    // 播放发射特效
    playLaunchEffect() {
        if (this.launchEffect && this.marble) {
            // 设置发射特效位置
            this.launchEffect.node.active = true;
            this.launchEffect.node.setPosition(this.marble.position);

            // 延迟一帧获取AI弹珠的发射方向（通过速度方向）
            this.scheduleOnce(() => {
                const rigidBody = this.marble.getComponent(RigidBody2D);
                if (rigidBody && rigidBody.linearVelocity.length() > 0.1) {
                    const velocity = rigidBody.linearVelocity;
                    const angle = Math.atan2(velocity.y, velocity.x) * 180 / Math.PI;
                    this.launchEffect.node.setRotationFromEuler(0, 0, angle - 90);
                }
            }, 0.1);

            // 播放发射动画
            const trackEntry = this.launchEffect.setAnimation(0, "animation", false);
            trackEntry.timeScale = 0.3;

            console.log("AI发射特效播放");
        }
    }

    // 碰撞时禁用特效
    onCollisionEnter(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact) {
        // 播放碰撞特效
        this.playCollisionEffect(contact);

        if (this.effectActive) {
            this.effectActive = false;
            this.node.active = false;
            console.log("特效禁用 - 发生碰撞");
        }
    }

    // 播放碰撞特效
    playCollisionEffect(contact: IPhysics2DContact) {
        if (!this.collisionEffect) return;

        // 检查冷却时间
        const currentTime = Date.now() / 1000;
        if (currentTime - this.lastCollisionTime < this.collisionCooldown) {
            return; // 还在冷却中，不播放特效
        }

        // 获取碰撞点位置
        const worldManifold = contact.getWorldManifold();
        if (worldManifold.points.length > 0) {
            // 设置碰撞特效位置为母弹位置
            this.collisionEffect.node.active = true;
            this.collisionEffect.node.setPosition(this.marble.position);

            // 播放碰撞动画
            const trackEntry = this.collisionEffect.setAnimation(0, "animation", false);
            trackEntry.timeScale = 1;

            // 更新上次碰撞时间
            this.lastCollisionTime = currentTime;

            console.log("AI碰撞特效播放");
        }
    }

    followMarble() {
        // 获取刚体
        let rigidbody = this.marble.getComponent(RigidBody2D);

        // 获取刚体速度
        let velocity = rigidbody.linearVelocity;

        // 设置位置跟随弹珠
        this.node.setPosition(this.marble.position);

        // 根据速度方向设置旋转
        if (velocity.length() > 0.1) { // 只在有明显移动时旋转
            const angle = Math.atan2(velocity.y, velocity.x) * 180 / Math.PI - 90;
            this.node.setRotationFromEuler(0, 0, angle);
        }
    }

    onDestroy() {
        // 清理事件监听
        director.off("aiMove", this.onMarbleLaunched, this);
        director.off('aiStop', this.onAIStop, this);

        // 清理碰撞监听
        if (this.marble) {
            const collider = this.marble.getComponent(Collider2D);
            if (collider) {
                collider.off(Contact2DType.BEGIN_CONTACT, this.onCollisionEnter, this);
            }
        }
    }

    update(deltaTime: number) {
        if (this.effectActive) {
            this.followMarble();
        }
    }
}
