import { _decorator, Component, Node, RigidBody2D, Vec2, sp, director, Collider2D, Contact2DType, IPhysics2DContact, resources } from 'cc';
import { AimLine } from '../physics/AimLine';
import { DataManager } from '../../Managers/DataManager';
const { ccclass, property } = _decorator;

@ccclass('MarbleSpine')
export class MarbleSpine extends Component {

    @property(Node)
    marble: Node = null;

    @property(sp.Skeleton)
    launchEffect: sp.Skeleton = null;

    @property(sp.Skeleton)
    collisionEffect: sp.Skeleton = null;

    @property(AimLine)
    aimLine: AimLine = null;

    // private spineSkeleton: sp.Skeleton = null;
    private effectActive: boolean = false;
    private lastCollisionTime: number = 0; // 上次碰撞时间
    private collisionCooldown: number = 1.0; // 碰撞特效冷却时间（秒）
    private dataManager: DataManager = null;

    // 特效资源路径映射
    private effectResourceMap = {
        launch: {
            1: "spine/发射001/001", // 火焰爆发
            2: "spine/烈焰破风1/发射/001", // 雷电激发
            3: "spine/烈焰破风1/发射/001", // 冰霜爆炸 (暂用同一资源)
            // 可以继续添加更多特效映射
        },
        collision: {
            101: "spine/烈焰破风1/碰撞/1", // 火花四溅
            102: "spine/烈焰破风1/碰撞/1", // 电光闪烁 (暂用同一资源)
            103: "spine/烈焰破风1/碰撞/1", // 冰晶破碎 (暂用同一资源)
            // 可以继续添加更多特效映射
        }
    };

    start() {
        // this.spineSkeleton = this.node.getComponent(sp.Skeleton);
        this.node.active = false;
        this.setupCollisionListener();

        // 初始化数据管理器
        this.dataManager = DataManager.getInstance();

        // 如果没有设置瞄准线引用，尝试自动查找
        if (!this.aimLine) {
            const aimLineNode = this.node.parent.getChildByName("AimLine");
            if (aimLineNode) {
                this.aimLine = aimLineNode.getComponent(AimLine);
            }
        }

        // 根据用户当前特效设置加载对应的SkeletonData
        this.loadUserEffects();

        // 监听弹珠发射事件
        director.on("playerMove", this.onMarbleLaunched, this);

        director.on('playerStop', this.onPlayerStop, this);
    }

    // 根据用户当前特效设置加载对应的SkeletonData
    loadUserEffects() {
        if (!this.dataManager || !this.dataManager.User) {
            console.warn("用户数据未初始化，使用默认特效");
            return;
        }

        // 加载发射特效
        this.loadLaunchEffect(this.dataManager.User.currentLaunchEffect);

        // 加载碰撞特效
        this.loadCollisionEffect(this.dataManager.User.currentCollisionEffect);
    }

    // 加载发射特效SkeletonData
    loadLaunchEffect(effectId: number) {
        const resourcePath = this.effectResourceMap.launch[effectId];
        if (!resourcePath) {
            console.warn(`未找到发射特效资源映射: ${effectId}`);
            return;
        }

        if (!this.launchEffect) {
            console.warn("发射特效组件未设置");
            return;
        }

        resources.load(resourcePath, sp.SkeletonData, (err, skeletonData) => {
            if (err) {
                console.error(`加载发射特效失败: ${resourcePath}`, err);
                return;
            }

            this.launchEffect.skeletonData = skeletonData;
            console.log(`发射特效加载成功: ${effectId} -> ${resourcePath}`);
        });
    }

    // 加载碰撞特效SkeletonData
    loadCollisionEffect(effectId: number) {
        const resourcePath = this.effectResourceMap.collision[effectId];
        if (!resourcePath) {
            console.warn(`未找到碰撞特效资源映射: ${effectId}`);
            return;
        }

        if (!this.collisionEffect) {
            console.warn("碰撞特效组件未设置");
            return;
        }

        resources.load(resourcePath, sp.SkeletonData, (err, skeletonData) => {
            if (err) {
                console.error(`加载碰撞特效失败: ${resourcePath}`, err);
                return;
            }

            this.collisionEffect.skeletonData = skeletonData;
            console.log(`碰撞特效加载成功: ${effectId} -> ${resourcePath}`);
        });
    }

    // 注册碰撞监听
    setupCollisionListener() {
        const collider = this.marble.getComponent(Collider2D);
        if (collider) {
            collider.on(Contact2DType.BEGIN_CONTACT, this.onCollisionEnter, this);
        }
    }

    onPlayerStop() {
        this.effectActive = false;
        this.node.active = false;
        console.log("特效禁用 - 玩家停止");
    }
    // 弹珠发射时启用特效
    onMarbleLaunched() {
        this.effectActive = true;
        this.playLaunchEffect();
        this.node.active = true;

        console.log("特效启用 - 弹珠发射");
    }

    // 播放发射特效
    playLaunchEffect() {
        if (this.launchEffect && this.marble) {
            // 设置发射特效位置
            this.launchEffect.node.active = true;
            this.launchEffect.node.setPosition(this.marble.position);

            // 获取瞄准线方向并设置特效旋转
            if (this.aimLine) {
                const direction = this.aimLine.getDirection();
                const angle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;
                this.launchEffect.node.setRotationFromEuler(0, 0, angle - 90);
            }

            // 播放发射动画
            const trackEntry = this.launchEffect.setAnimation(0, "animation", false);
            trackEntry.timeScale = 0.3;

            console.log("发射特效播放");
        }
    }

    // 碰撞时禁用特效
    onCollisionEnter(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact) {

        if (this.effectActive) {
            this.effectActive = false;
            this.node.active = false;
            console.log("特效禁用 - 发生碰撞");
        }
        this.playCollisionEffect(contact);
    }


    // 播放碰撞特效
    playCollisionEffect(contact: IPhysics2DContact) {
        // console.log("碰撞特效播放");
        if (!this.collisionEffect) return;

        // 检查冷却时间
        const currentTime = Date.now() / 1000;
        if (currentTime - this.lastCollisionTime < this.collisionCooldown) {
            return; // 还在冷却中，不播放特效
        }

        // 获取碰撞点位置
        const worldManifold = contact.getWorldManifold();
        if (worldManifold.points.length > 0) {
            const collisionPoint = worldManifold.points[0];

            // 设置碰撞特效位置为母弹位置
            this.collisionEffect.node.active = true;
            this.collisionEffect.node.setPosition(this.marble.position);

            // 播放碰撞动画
            const trackEntry = this.collisionEffect.setAnimation(0, "animation", false);
            trackEntry.timeScale = 1;

            // 更新上次碰撞时间
            this.lastCollisionTime = currentTime;

            console.log("碰撞特效播放");
        }
    }

    followMarble() {

        // 获取刚体
        let rigidbody = this.marble.getComponent(RigidBody2D);

        // 获取刚体速度
        let velocity = rigidbody.linearVelocity;

        // 设置位置跟随弹珠
        this.node.setPosition(this.marble.position);

        // 根据速度方向设置旋转
        if (velocity.length() > 0.1) { // 只在有明显移动时旋转
            const angle = Math.atan2(velocity.y, velocity.x) * 180 / Math.PI - 90;
            this.node.setRotationFromEuler(0, 0, angle);

        }
    }

    onDestroy() {
        // 清理事件监听
        director.off("marble-launched", this.onMarbleLaunched, this);

        // 清理碰撞监听
        if (this.marble) {
            const collider = this.marble.getComponent(Collider2D);
            if (collider) {
                collider.off(Contact2DType.BEGIN_CONTACT, this.onCollisionEnter, this);
            }
        }
    }

    update(deltaTime: number) {
        if (this.effectActive) {
            this.followMarble();
        }
    }
}


