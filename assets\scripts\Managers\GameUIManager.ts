import { _decorator, <PERSON><PERSON>, Component, director, Label, Node, resources, Skeleton, sp, Sprite, SpriteFrame, tween, UIOpacity, ProgressBar } from 'cc';
const { ccclass, property } = _decorator;
import { PlayeMarble } from '../gameController/physics/PlayeMarble';
import { AiMarble } from '../gameController/physics/AiMarble';
import { AimLine } from '../gameController/physics/AimLine';
import { PowerBar } from '../gameController/physics/PowerBar';
import { Finger } from '../gameController/physics/Finger';
import { TargetArea } from '../gameController/physics/TargetArea';
import { CameraController } from '../gameController/ui/CameraController';
import { Floor } from '../gameController/physics/Floor';

@ccclass('UIManager')
export class UIManager extends Component {

    @property(PlayeMarble)
    playerMarble: PlayeMarble = null

    @property(AiMarble)
    aiMarble: AiMarble = null

    @property(AimLine)
    aimLine: AimLine = null

    @property(PowerBar)
    powerBar: PowerBar = null

    @property(Finger)
    finger: Finger = null

    @property(TargetArea)
    targetArea: TargetArea = null

    @property(Node)
    PlayerDiceAnimation: Node = null

    @property(Node)
    AiDiceAnimation: Node = null

    @property(Node)
    TopUI: Node = null

    @property(Node)
    winUI: Node = null

    @property(Node)
    loseUI: Node = null

    @property(Node)
    setingUI: Node = null

    @property(CameraController)
    cameraController: CameraController = null

    @property(Node)
    Weather: Node = null

    @property(Floor)
    floor: Floor = null

    @property(Node)
    setting: Node = null;

    @property(Node)
    tipNode: Node = null;

    @property(Node)
    loadingNode: Node = null;

    // 倒计时相关
    private countdownTimer: number = 0;
    private isCountdownActive: boolean = false;
    private currentCountdown: number = 20;


    public static _instance: UIManager = null;

    public static getInstance() {
        return this._instance;
    }

    onLoad() {
        if (UIManager._instance !== null) {
            console.error("UIManager already exists!");
            return;
        }
        UIManager._instance = this;
    }

    start() {
        // 监听数据初始化完成
        director.on("gameInit_", this.onGameInit, this);
        // 监听顺序阶段
        director.on("orderState", this.onOrderState, this);
        // 监听玩家回合
        director.on("playerTurn", this.onPlayerTurn, this);
        // 监听玩家移动
        director.on("playerMove", this.onPlayerMove, this);
        // 监听AI回合
        director.on("aiTurn", this.onAiTurn, this);
        // 监听AI移动
        director.on("aiMove", this.onAiMove, this);
        // 监听游戏结束
        director.on("gameOver", this.onGameOver, this);

    }


    onGameOver(winner: string, rewardCount: number) {
        console.log(`游戏结束，胜利者: ${winner}`);

        if (winner === "player") {
            this.winUI.getChildByName('button').getChildByName('receive').on('click', this.recevieResult, this);
            this.winUI.getChildByName('Result').getChildByName('reward').getChildByName('tip').getComponent(Label).string = `${rewardCount}`;
            this.winUI.active = true;
            this.winUI.setPosition(this.cameraController.node.position);
        } else {
            this.loseUI.getChildByName('button').getChildByName('receive').on('click', this.recevieResult, this);
            this.loseUI.getChildByName('reward').getChildByName('tip').getComponent(Label).string = `${rewardCount}`;
            this.loseUI.active = true;
            this.loseUI.setPosition(this.cameraController.node.position);
        }

    }
    recevieResult() {
        this.preloadHomeSceneWithProgress();
    }

    // 预加载主页场景并显示加载进度
    private preloadHomeSceneWithProgress() {
        // 显示加载页面
        if (this.loadingNode) {
            this.loadingNode.active = true;
            this.loadingNode.setPosition(this.cameraController.node.position);
        }

        // 开始预加载主页场景
        director.preloadScene('HomeScene',
            (completedCount: number, totalCount: number) => {
                // 计算加载进度
                const progress = completedCount / totalCount;
                this.updateLoadingProgress(progress);
            },
            (error: Error | null) => {
                if (error) {
                    console.error('预加载主页场景失败:', error);
                    // 如果预加载失败，直接跳转
                    director.loadScene('HomeScene');
                    return;
                }
                // 预加载完成，延迟一下再跳转以显示100%进度
                setTimeout(() => {
                    director.loadScene('HomeScene');
                }, 500);
            }
        );
    }

    // 更新加载进度
    private updateLoadingProgress(progress: number) {
        if (!this.loadingNode) return;

        // 查找progress节点下的Label组件来显示百分比
        const progressNode = this.loadingNode.getChildByName('progress');
        if (progressNode) {
            const progressLabel = progressNode.getComponent(Label);
            if (progressLabel) {
                const percentage = Math.floor(progress * 100);
                progressLabel.string = `${percentage}%`;
            }
        }
    }

    onOrderState(playerNumber: Number, aiNumber: Number, Firster: string) {
        console.log(`玩家: ${playerNumber}, AI: ${aiNumber}, 先手: ${Firster}`);

        this.PlayerDiceAnimation.getComponent(sp.Skeleton).setSkin(`0${playerNumber}`);
        this.AiDiceAnimation.getComponent(sp.Skeleton).setSkin(`0${aiNumber}`);

        this.AiDiceAnimation.setScale(-1, 1);

        // 设置动画播放速度（0.5表示半速播放，2表示双倍速度）
        this.PlayerDiceAnimation.getComponent(sp.Skeleton).timeScale = 0.5;
        this.AiDiceAnimation.getComponent(sp.Skeleton).timeScale = 0.5;

        this.PlayerDiceAnimation.active = true;
        this.AiDiceAnimation.active = true;
        // 延迟1s
        setTimeout(() => {
            director.emit("orderStateInited");
        },3000);
        
    }

    onGameInit(generateCount: number) {
        console.log("隐藏两者节点");
        this.hidePlayerUI();
        this.hideAiNode();
        this.targetArea.generateTargetMarbles(generateCount);
        // 生成障碍物 - 根据数量生成
        this.targetArea.generateObstacles(generateCount);
        this.tipNode.active = false;
        this.PlayerDiceAnimation.active = false;
        this.AiDiceAnimation.active = false;

        director.emit("uiManagerInited");
    }

    onPlayerTurn() {
        this.showTipWithFadeEffect('你的回合');
        this.setting.active = true;
        this.showPlayerUI();
        this.startCountdown(); // 开始倒计时
        // this.hideAiNode();
    }

    onPlayerMove() {
        this.setting.active = false;
        this.hideActionUI();
        this.stopCountdown();
    }


    onAiTurn() {
        this.showTipWithFadeEffect('对手回合');
        this.startCountdown(); // 开始倒计时
        // this.hidePlayerUI();
        this.hideActionUI();
        this.showAiNode();
    }

    onAiMove() {
        this.stopCountdown();
    }

    setcountdown(time: number) {
        this.Weather.getChildByName('countdown').getComponent(Label).string = time.toString();
    }

    // 开始倒计时
    startCountdown() {
        this.currentCountdown = 20; // 重置倒计时
        this.isCountdownActive = true;
        this.countdownTimer = 0;
        this.setcountdown(this.currentCountdown);
    }

    // 停止倒计时
    stopCountdown() {
        this.isCountdownActive = false;
        this.countdownTimer = 0;
    }

    // 倒计时结束处理
    onCountdownEnd() {
        console.log("时间到！强制结束回合");
        this.stopCountdown();

        // 根据当前玩家强制结束回合
        if (this.isPlayerTurn()) {
            // 玩家时间到，强制发射或跳过回合
            director.emit("playerTimeOut");
        }
    }

    // 检查是否是玩家回合
    private isPlayerTurn(): boolean {
        // 通过检查玩家UI是否显示来判断
        return this.playerMarble.node.active && this.powerBar.node.active;
    }

    // 显示提示并实现渐入1秒后消失的效果
    showTipWithFadeEffect(spriteFrameName: string) {
        const address = "gameUI/" + spriteFrameName + "/spriteFrame";
        // 设置精灵图片
        resources.load(address, SpriteFrame, (err, spriteFrame) => {
            this.tipNode.getComponent(Sprite).spriteFrame = spriteFrame;
        });

        // this.tipNode.setPosition(this.cameraController.node.position.x, this.cameraController.node.position.y + 820);

        // 获取或添加UIOpacity组件
        let uiOpacity = this.tipNode.getComponent(UIOpacity);
        if (!uiOpacity) {
            uiOpacity = this.tipNode.addComponent(UIOpacity);
        }

        // 初始设置为完全透明
        uiOpacity.opacity = 0;
        this.tipNode.active = true;

        // 渐入动画 (0.3秒渐入)
        tween(uiOpacity)
            .to(0.3, { opacity: 255 })
            .delay(2.0) // 显示1秒
            .to(0.3, { opacity: 0 }) // 0.3秒渐出
            .call(() => {
                this.tipNode.active = false; // 动画结束后隐藏节点
            })
            .start();
    }

    // 隐藏玩家UI组件
    hidePlayerUI() {
        this.playerMarble.hide();
        this.powerBar.hide();
        this.aimLine.hide();
        this.finger.hide();
    }
    //隐藏操作节点
    hideActionUI() {
        this.powerBar.hide();
        this.aimLine.hide();
        this.finger.hide();
    }
    // 隐藏AI节点
    hideAiNode() {
        this.aiMarble.hide();
    }
    //显示玩家UI组件
    showPlayerUI() {
        this.playerMarble.init();
        this.powerBar.init();
        this.aimLine.init();
        this.finger.init();
    }
    // 显示AI节点
    showAiNode() {
        this.aiMarble.init();
    }
    setTopUIPosition() {
        this.TopUI.setPosition(this.cameraController.node.position.x, this.cameraController.node.position.y + 810);
        this.Weather.setPosition(this.cameraController.node.position.x + 450, this.cameraController.node.position.y + 900);
    }
    update(deltaTime: number) {
        this.setTopUIPosition();

        // 处理倒计时
        if (this.isCountdownActive) {
            this.countdownTimer += deltaTime;

            // 每秒更新一次倒计时显示
            if (this.countdownTimer >= 1.0) {
                this.countdownTimer = 0;
                this.currentCountdown--;
                this.setcountdown(this.currentCountdown);

                // 倒计时结束
                if (this.currentCountdown <= 0) {
                    this.onCountdownEnd();
                }
            }
        }
    }
}


